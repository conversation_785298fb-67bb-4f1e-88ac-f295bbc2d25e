<template id="question-form-template-first-click-test">
  <div class="form-group">
    <fc-label params="text: question.translator.t('Изображение'), required: true"></fc-label>
    
    <!-- ko if: !question.imagePreview() -->
    <div class="first-click-test-upload-area" data-bind="
      dnd: function(files) { question.imageLoader.loadFiles(files) },
      dndDisabled: question.isFullBlocked,
      css: { 'disabled': question.isFullBlocked }
    ">
      <div class="first-click-test-upload-content">
        <svg-icon params="name: 'image', size: 48"></svg-icon>
        <div class="first-click-test-upload-text">
          <div class="first-click-test-upload-title" data-bind="text: question.translator.t('Перетащите изображение сюда')"></div>
          <div class="first-click-test-upload-subtitle" data-bind="text: question.translator.t('или выберите файл')"></div>
        </div>
        
        <!-- ko let: { fileInput: ko.observable(null) } -->
        <input data-bind="
          element: fileInput,
          event: {
            change: function(_, event) {
              const file = event.target.files[0];
              if (file) {
                question.imageLoader.loadFiles([file]);
              }
              event.target.value = '';
            }
          },
          attr: { accept: 'image/*' }
        " type="file" hidden>
        
        <button type="button" class="f-btn f-btn--primary" data-bind="
          click: function() { $(fileInput()).trigger('click'); },
          disable: question.isFullBlocked
        ">
          <span data-bind="text: question.translator.t('Выбрать файл')"></span>
        </button>
        <!-- /ko -->
      </div>
      
      <div class="first-click-test-upload-formats">
        <small data-bind="text: question.translator.t('Поддерживаемые форматы: JPG, PNG, GIF, SVG (до 5 МБ)')"></small>
      </div>
    </div>
    <!-- /ko -->
    
    <!-- ko if: question.imagePreview() -->
    <div class="first-click-test-image-preview">
      <div class="first-click-test-image-container">
        <img data-bind="attr: { src: question.imagePreview() }" class="first-click-test-image">
        
        <div class="first-click-test-image-overlay">
          <button type="button" class="f-btn f-btn--secondary f-btn--sm" data-bind="
            click: question.openFullscreen.bind(question)
          ">
            <svg-icon params="name: 'expand'"></svg-icon>
            <span data-bind="text: question.translator.t('Полный размер')"></span>
          </button>
          
          <button type="button" class="f-btn f-btn--secondary f-btn--sm" data-bind="
            click: question.openClickAreasDialog.bind(question)
          ">
            <svg-icon params="name: 'cursor-click'"></svg-icon>
            <span data-bind="text: question.translator.t('Области клика')"></span>
          </button>
          
          <button type="button" class="f-btn f-btn--danger f-btn--sm" data-bind="
            click: question.removeImage.bind(question),
            disable: question.isFullBlocked
          ">
            <svg-icon params="name: 'trash'"></svg-icon>
            <span data-bind="text: question.translator.t('Удалить')"></span>
          </button>
        </div>
      </div>
      
      <!-- ko if: question.clickAreas().length -->
      <div class="first-click-test-areas-info">
        <small data-bind="text: question.translator.t('Областей клика: ') + question.clickAreas().length"></small>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
    
    <!-- ko if: question.imageLoader.error() -->
    <div class="form-error" data-bind="text: question.imageLoader.error().text"></div>
    <!-- /ko -->
  </div>

  <hr class="mx-0">

  <!-- Mobile Display Settings -->
  <div class="form-group">
    <fc-label params="text: question.translator.t('Отображение на мобильных устройствах')"></fc-label>
    <fc-radio-group params="
      value: question.mobileDisplay,
      options: [
        { value: 'width', label: question.translator.t('По ширине экрана') },
        { value: 'height', label: question.translator.t('По высоте экрана') }
      ],
      disabled: question.isFullBlocked
    "></fc-radio-group>
  </div>

  <hr class="mx-0">

  <!-- Click Count Settings -->
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <fc-label params="text: question.translator.t('Минимальное количество кликов'), required: true"></fc-label>
        <fc-input params="
          value: question.minClicks,
          type: 'number',
          min: 1,
          max: 999,
          placeholder: '1',
          disabled: question.isFullBlocked,
          validationOptions: { insertMessages: false }
        "></fc-input>
      </div>
    </div>
    
    <div class="col-md-6">
      <div class="form-group">
        <fc-label params="text: question.translator.t('Максимальное количество кликов')"></fc-label>
        <fc-input params="
          value: question.maxClicks,
          type: 'number',
          min: 1,
          max: 999,
          placeholder: question.translator.t('Без ограничений'),
          disabled: question.isFullBlocked,
          validationOptions: { insertMessages: false }
        "></fc-input>
      </div>
    </div>
  </div>

  <hr class="mx-0">

  <!-- Additional Settings -->
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <fc-label params="text: question.translator.t('Время показа (секунды)')"></fc-label>
        <fc-input params="
          value: question.displayTime,
          type: 'number',
          min: 1,
          max: 300,
          placeholder: question.translator.t('Без ограничений'),
          disabled: question.isFullBlocked
        "></fc-input>
        <small class="form-text text-muted" data-bind="text: question.translator.t('Оставьте пустым для неограниченного времени')"></small>
      </div>
    </div>
    
    <div class="col-md-6">
      <div class="form-group">
        <fc-label params="text: question.translator.t('Текст кнопки завершения')"></fc-label>
        <fc-input params="
          value: question.buttonText,
          placeholder: question.translator.t('Завершить'),
          maxlength: 50,
          disabled: question.isFullBlocked
        "></fc-input>
        <small class="form-text text-muted" data-bind="text: question.translator.t('Оставьте пустым для стандартного текста')"></small>
      </div>
    </div>
  </div>

  <hr class="mx-0">

  <!-- Advanced Options -->
  <div class="form-group">
    <fc-switch params="
      checked: question.allowClickCancel,
      label: question.translator.t('Разрешить отмену кликов'),
      disabled: question.isFullBlocked
    "></fc-switch>
    <small class="form-text text-muted" data-bind="text: question.translator.t('Пользователи смогут отменять свои клики')"></small>
  </div>

  <hr class="mx-0">

  <!-- Skip Option -->
  <div class="form-group">
    <fc-switch params="
      checked: question.skipOption,
      label: question.translator.t('Пропуск вопроса'),
      disabled: question.isFullBlocked
    "></fc-switch>
  </div>

  <!-- ko template: {
    foreach: templateIf(question.skipOption(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400)
  } -->
  <div class="form-group">
    <fc-label params="text: question.translator.t('Текст кнопки пропуска')"></fc-label>
    <fc-input params="
      value: question.skipText,
      placeholder: question.translator.t('Пропустить'),
      maxlength: 50,
      disabled: question.isFullBlocked
    "></fc-input>
  </div>
  <!-- /ko -->

  <hr class="mx-0">

  <!-- Comment Option -->
  <div class="form-group">
    <fc-switch params="
      checked: question.commentOption,
      label: question.translator.t('Поле для комментария'),
      disabled: question.isFullBlocked
    "></fc-switch>
  </div>
</template>
