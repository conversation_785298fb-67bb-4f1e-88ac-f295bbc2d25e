<template id="question-form-template-first-click-test">
  <div class="question-form-template-first-click-test-main-container">
    <div class="form-group form-group--first-click-test-image-container">
      <fc-label params="text: question.translator.t('Загрузка изображения'), hint: question.translator.t('Загрузка изображения')"></fc-label>

      <div class="d-flex align-items-start">
        <media-load-button params="loader: question.imageLoader, disabled: question.isFullBlocked">
          <svg-icon params="name: 'clip'"></svg-icon>
          <div class="mt-5p" style="line-height: 1.15;">
            jpg, jpeg, png, gif, svg
            <br>
            до 5 Мб
          </div>
        </media-load-button>

        <!-- ko if: question.imagePreview() -->
        <div class="ml-3 flex-grow-1">
          <button type="button" class="f-btn f-btn--secondary f-btn--sm mb-2" data-bind="
            click: question.openClickAreasDialog.bind(question)
          ">
            <span data-bind="text: question.translator.t('Изменить области клика') + ' (' + question.clickAreas().length + ')'"></span>
          </button>

          <div class="f-color-service f-fs-1 mb-20p mt-10p" data-bind="text: question.translator.t('Отметьте области, по которым хотели бы получить статистику, либо собирайте клики и анализируйте по тепловой карте')"></div>
        </div>
        <!-- /ko -->
      </div>

      <file-loader-error params="error: question.imageLoader.error"></file-loader-error>
    </div>

    <hr class="mx-0">

    <!-- Mobile Display Settings -->
    <div class="form-group">
      <fc-label params="text: question.translator.t('Отображение на мобильных устройствах'), hint: question.translator.t('Отображение на мобильных устройствах')"></fc-label>
      <fc-select params="
        value: question.mobileDisplay,
        options: [
          { id: 'width', text: question.translator.t('По ширине экрана') },
          { id: 'height', text: question.translator.t('По высоте экрана') }
        ],
        disabled: question.isFullBlocked
      "></fc-select>
      <div class="f-color-service f-fs-1 mb-20p mt-10p" data-bind="text: question.translator.t('По умолчанию выбрано значение По ширине')"></div>
    </div>

    <hr class="mx-0">

    <!-- Click Count Settings -->
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <fc-label params="text: question.translator.t('Минимальное количество кликов'), hint: question.translator.t('Минимальное количество кликов')"></fc-label>
          <fc-input params="
            value: question.minClicks,
            type: 'number',
            min: 1,
            max: 999,
            placeholder: '1',
            disabled: question.isFullBlocked,
            validationOptions: { insertMessages: false }
          "></fc-input>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group">
          <fc-label params="text: question.translator.t('Максимальное количество кликов'), hint: question.translator.t('Максимальное количество кликов')"></fc-label>
          <fc-input params="
            value: question.maxClicks,
            type: 'number',
            min: 1,
            max: 999,
            placeholder: question.translator.t('Без ограничений'),
            disabled: question.isFullBlocked,
            validationOptions: { insertMessages: false }
          "></fc-input>
        </div>
      </div>
    </div>

    <hr class="mx-0">

    <!-- Additional Settings -->
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <fc-label params="text: question.translator.t('Время показа (секунды)'), hint: question.translator.t('Время показа (секунды)')"></fc-label>
          <fc-input params="
            value: question.displayTime,
            type: 'number',
            min: 1,
            max: 300,
            placeholder: question.translator.t('Без ограничений'),
            disabled: question.isFullBlocked
          "></fc-input>
          <div class="f-color-service f-fs-1 mb-20p mt-10p" data-bind="text: question.translator.t('Оставьте пустым для неограниченного времени')"></div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group">
          <fc-label params="text: question.translator.t('Текст кнопки завершения'), hint: question.translator.t('Текст кнопки завершения')"></fc-label>
          <fc-input params="
            value: question.buttonText,
            placeholder: question.translator.t('Завершить'),
            maxlength: 50,
            disabled: question.isFullBlocked
          "></fc-input>
          <div class="f-color-service f-fs-1 mb-20p mt-10p" data-bind="text: question.translator.t('Оставьте пустым для стандартного текста')"></div>
        </div>
      </div>
    </div>

    <hr class="mx-0">

    <!-- Advanced Options -->
    <div class="form-group">
      <fc-checkbox params="
        checked: question.allowClickCancel,
        label: question.translator.t('Разрешить отмену кликов'),
        hint: question.translator.t('Разрешить отмену кликов'),
        disabled: question.isFullBlocked
      "></fc-checkbox>
      <div class="f-color-service f-fs-1 mb-20p mt-10p" data-bind="text: question.translator.t('Пользователи смогут отменять свои клики')"></div>
    </div>

    <hr class="mx-0">

    <!-- Skip Option -->
    <div class="form-group">
      <fc-switch params="
        checked: question.skipOption,
        label: question.translator.t('Пропуск вопроса'),
        disabled: question.isFullBlocked
      "></fc-switch>
    </div>

    <!-- ko template: {
      foreach: templateIf(question.skipOption(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <fc-label params="text: question.translator.t('Текст кнопки пропуска'), hint: question.translator.t('Текст кнопки пропуска')"></fc-label>
      <fc-input params="
        value: question.skipText,
        placeholder: question.translator.t('Пропустить'),
        maxlength: 50,
        disabled: question.isFullBlocked
      "></fc-input>
    </div>
    <!-- /ko -->

    <hr class="mx-0">

    <!-- Comment Option -->
    <div class="form-group">
      <fc-switch params="
        checked: question.commentOption,
        label: question.translator.t('Поле для комментария'),
        disabled: question.isFullBlocked
        "></fc-switch>
    </div>
  </div>
</template>
