import { FIRST_CLICK_TEST } from "Data/question-types";

export default {
  type: FIRST_CLICK_TEST,
  
  format(data) {
    const result = {
      // Basic question data
      main_question_type: FIRST_CLICK_TEST,
      name: data.name || "",
      description: data.description || "",
      is_required: data.required ? "1" : "0",
      
      // Image data
      image_file_id: null,
      image_url: "",
      
      // Click areas as JSON string
      click_areas: JSON.stringify(data.clickAreas || []),
      
      // First Click Test settings
      mobile_display: data.mobileDisplay || "width",
      min_clicks: data.minClicks || 1,
      max_clicks: data.maxClicks || null,
      display_time: data.displayTime || null,
      button_text: data.buttonText || "",
      allow_click_cancel: data.allowClickCancel ? "1" : "0",
      
      // Standard options
      skip_option: data.skipOption ? "1" : "0",
      skip_text: data.skipText || "",
      comment_option: data.commentOption ? "1" : "0",
    };

    // Handle image data
    if (data.imageId) {
      result.image_file_id = data.imageId;
    }
    
    if (data.imageUrl) {
      result.image_url = data.imageUrl;
    }

    // Ensure numeric values are properly formatted
    if (result.min_clicks) {
      result.min_clicks = parseInt(result.min_clicks) || 1;
    }

    if (result.max_clicks !== null && result.max_clicks !== "") {
      result.max_clicks = parseInt(result.max_clicks) || null;
    }

    if (result.display_time !== null && result.display_time !== "") {
      result.display_time = parseInt(result.display_time) || null;
    }

    // Clean up empty values
    Object.keys(result).forEach(key => {
      if (result[key] === "" && key !== "button_text" && key !== "skip_text") {
        result[key] = null;
      }
    });

    return result;
  }
};
